<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - VAITH Admin</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        console.log('JavaScript is working - page loaded');
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
        });
    </script>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link active">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">Product Management</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Page Header -->
                <div class="page-header">
                    <h2 class="page-title">Products</h2>
                    <p class="page-subtitle">Manage your product catalog and inventory</p>
                </div>

                <!-- Product Stats -->
                <div class="dashboard-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin-bottom: 2rem;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalProductsCount">0</h3>
                                <p>Total Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="activeProductsCount">0</h3>
                                <p>Active Products</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="outOfStockCount">0</h3>
                                <p>Out of Stock</p>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="lowStockCount">0</h3>
                                <p>Low Stock</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Product Button -->
                <div class="page-actions" style="margin-bottom: 2rem;">
                    <button class="btn btn-primary" id="addProductBtn" onclick="alert('Button clicked!'); openAddProductModal();">
                        <i class="fas fa-plus"></i> Add New Product
                    </button>
                    <button class="btn btn-secondary" onclick="alert('Test button works!');" style="margin-left: 1rem;">
                        Test Button
                    </button>
                    <button class="btn btn-outline" onclick="document.getElementById('addProductModal').style.display='flex'; document.getElementById('overlay').style.display='block';" style="margin-left: 1rem;">
                        Direct Modal Open
                    </button>
                </div>

                <!-- Debug Info -->
                <div style="background: #f8f9fa; padding: 1rem; margin-bottom: 2rem; border-radius: 0.5rem; border: 1px solid #dee2e6;">
                    <h4 style="margin: 0 0 1rem 0; color: #495057;">Debug Information</h4>
                    <p style="margin: 0.5rem 0; font-family: monospace; font-size: 0.875rem;">
                        <strong>Instructions:</strong><br>
                        1. Click "Test Button" - should show alert<br>
                        2. Click "Direct Modal Open" - should open modal directly<br>
                        3. Click "Add New Product" - should show alert then open modal<br>
                        4. Open browser console (F12) to see debug messages
                    </p>
                    <button onclick="console.log('Console test:', document.getElementById('addProductModal'), document.getElementById('overlay'))" style="padding: 0.5rem 1rem; margin-top: 0.5rem; background: #007bff; color: white; border: none; border-radius: 0.25rem;">
                        Log Modal Elements
                    </button>
                </div>

                <!-- Filters and Search -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">All Products</h3>
                        <div class="table-actions">
                            <div class="search-box" style="margin-right: 1rem;">
                                <input type="text" id="productSearch" placeholder="Search products..." style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem; background: var(--input-bg); color: var(--text-color);">
                            </div>
                            <select id="categoryFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Categories</option>
                                <option value="men">Men</option>
                                <option value="women">Women</option>
                                <option value="accessories">Accessories</option>
                                <option value="shoes">Shoes</option>
                            </select>
                            <select id="statusFilter" class="form-select" style="margin-right: 1rem; padding: 0.5rem; min-width: 120px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="draft">Draft</option>
                            </select>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="min-width: 200px;">Product</th>
                                    <th style="min-width: 100px;">SKU</th>
                                    <th style="min-width: 120px;">Category</th>
                                    <th style="min-width: 100px;">Price</th>
                                    <th style="min-width: 80px;">Stock</th>
                                    <th style="min-width: 100px;">Status</th>
                                    <th style="min-width: 100px;">Rating</th>
                                    <th style="min-width: 150px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Product Details Modal -->
    <div class="modal" id="productModal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="modalTitle">Product Details</h3>
                <button class="close-modal" id="closeProductModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="productModalBody">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal" id="addProductModal" style="display: none;">
        <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header">
                <h3>Add New Product</h3>
                <button class="close-modal" id="closeAddProductModal" onclick="closeAddProductModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addProductForm" style="display: grid; gap: 1.5rem;">
                    <!-- Basic Information -->
                    <div style="display: grid; gap: 1rem;">
                        <h4 style="margin: 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">Basic Information</h4>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Product Name *</label>
                                <input type="text" id="productName" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Brand</label>
                                <input type="text" id="productBrand" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                            </div>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Description</label>
                            <textarea id="productDescription" rows="3" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary); resize: vertical;"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Category *</label>
                                <select id="productCategory" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                                    <option value="">Select Category</option>
                                    <option value="men">Men</option>
                                    <option value="women">Women</option>
                                    <option value="accessories">Accessories</option>
                                    <option value="shoes">Shoes</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Status</label>
                                <select id="productStatus" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="draft">Draft</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">SKU</label>
                                <input type="text" id="productSku" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                            </div>
                        </div>
                    </div>

                    <!-- Pricing & Inventory -->
                    <div style="display: grid; gap: 1rem;">
                        <h4 style="margin: 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">Pricing & Inventory</h4>

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Price *</label>
                                <input type="number" id="productPrice" step="0.01" min="0" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Original Price</label>
                                <input type="number" id="productOriginalPrice" step="0.01" min="0" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Stock Quantity *</label>
                                <input type="number" id="productStock" min="0" required style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                            </div>
                        </div>
                    </div>

                    <!-- Product Images -->
                    <div style="display: grid; gap: 1rem;">
                        <h4 style="margin: 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">Product Images</h4>

                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Image URLs (one per line)</label>
                            <textarea id="productImages" rows="4" placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary); resize: vertical;"></textarea>
                            <small style="color: var(--admin-text-tertiary); font-size: 0.875rem;">Enter image URLs, one per line. First image will be the main product image.</small>
                        </div>
                    </div>

                    <!-- Product Variants -->
                    <div style="display: grid; gap: 1rem;">
                        <h4 style="margin: 0; color: var(--admin-text-primary); border-bottom: 1px solid var(--admin-border); padding-bottom: 0.5rem;">Product Variants</h4>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Available Sizes</label>
                                <input type="text" id="productSizes" placeholder="XS, S, M, L, XL" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                                <small style="color: var(--admin-text-tertiary); font-size: 0.875rem;">Separate sizes with commas</small>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: var(--admin-text-primary);">Available Colors</label>
                                <input type="text" id="productColors" placeholder="Black, White, Blue, Red" style="width: 100%; padding: 0.75rem; border: 1px solid var(--admin-border); border-radius: 0.5rem; background: var(--admin-input-bg); color: var(--admin-text-primary);">
                                <small style="color: var(--admin-text-tertiary); font-size: 0.875rem;">Separate colors with commas</small>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div style="display: flex; gap: 1rem; justify-content: flex-end; padding-top: 1rem; border-top: 1px solid var(--admin-border);">
                        <button type="button" class="btn btn-secondary" onclick="closeAddProductModal()">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitAddProductForm()">
                            <i class="fas fa-plus"></i> Add Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" style="display: none;" onclick="closeAddProductModal()"></div>

    <!-- Modal and Dropdown Styles -->
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            max-height: 90vh;
            overflow-y: auto;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-color);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
        }

        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }



        .stock-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stock-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .stock-high { background: var(--success-color); }
        .stock-medium { background: var(--warning-color); }
        .stock-low { background: var(--error-color); }
        .stock-out { background: var(--text-light); }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>

    <script>
        // Simple function to open add product modal - works immediately
        function openAddProductModal() {
            console.log('Opening add product modal...');
            const modal = document.getElementById('addProductModal');
            const overlay = document.getElementById('overlay');

            if (modal && overlay) {
                modal.style.display = 'flex';
                overlay.style.display = 'block';
                console.log('Modal opened successfully');

                // Generate SKU
                const timestamp = Date.now().toString().slice(-6);
                const random = Math.random().toString(36).substring(2, 5).toUpperCase();
                const sku = `SKU-${random}${timestamp}`;

                const skuInput = document.getElementById('productSku');
                if (skuInput) {
                    skuInput.value = sku;
                }
            } else {
                console.error('Modal elements not found');
                alert('Error: Could not open add product modal');
            }
        }

        // Simple function to close modal
        function closeAddProductModal() {
            const modal = document.getElementById('addProductModal');
            const overlay = document.getElementById('overlay');

            if (modal && overlay) {
                modal.style.display = 'none';
                overlay.style.display = 'none';

                // Reset form
                const form = document.getElementById('addProductForm');
                if (form) {
                    form.reset();
                }
            }
        }

        // Function to submit the add product form
        function submitAddProductForm() {
            console.log('Submitting add product form...');

            // Get form data
            const name = document.getElementById('productName').value.trim();
            const brand = document.getElementById('productBrand').value.trim() || 'VAITH';
            const description = document.getElementById('productDescription').value.trim();
            const category = document.getElementById('productCategory').value;
            const status = document.getElementById('productStatus').value;
            const sku = document.getElementById('productSku').value.trim();
            const price = parseFloat(document.getElementById('productPrice').value);
            const originalPrice = document.getElementById('productOriginalPrice').value ?
                parseFloat(document.getElementById('productOriginalPrice').value) : null;
            const stock = parseInt(document.getElementById('productStock').value);
            const images = document.getElementById('productImages').value.trim().split('\n')
                .map(url => url.trim()).filter(url => url);
            const sizes = document.getElementById('productSizes').value.trim().split(',')
                .map(size => size.trim()).filter(size => size);
            const colors = document.getElementById('productColors').value.trim().split(',')
                .map(color => color.trim()).filter(color => color);

            // Validate required fields
            if (!name || !category || !price || stock < 0) {
                alert('Please fill in all required fields (Name, Category, Price, Stock)');
                return;
            }

            // Add default image if none provided
            if (images.length === 0) {
                images.push('https://via.placeholder.com/400x500?text=No+Image');
            }

            // Add default sizes and colors if none provided
            if (sizes.length === 0) {
                sizes.push('One Size');
            }
            if (colors.length === 0) {
                colors.push('Default');
            }

            // Create product object
            const product = {
                id: Date.now(),
                name: name,
                brand: brand,
                description: description,
                category: category,
                status: status,
                sku: sku,
                price: price,
                originalPrice: originalPrice,
                stock: stock,
                images: images,
                sizes: sizes,
                colors: colors,
                rating: 0,
                reviews: 0,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString()
            };

            // Save to localStorage
            try {
                let products = JSON.parse(localStorage.getItem('vaith_products')) || [];
                products.push(product);
                localStorage.setItem('vaith_products', JSON.stringify(products));

                alert(`Product "${name}" added successfully!`);
                closeAddProductModal();

                // Refresh the page to show the new product
                window.location.reload();

            } catch (error) {
                console.error('Error saving product:', error);
                alert('Error saving product. Please try again.');
            }
        }

        let currentProducts = [];
        let filteredProducts = [];

        // File protocol compatible initialization
        function initializeAdminProducts() {
            console.log('Admin products page - initializing for file:// protocol compatibility');

            // Initialize admin manager with fallback for file:// protocol
            if (typeof window.adminManager === 'undefined') {
                // Create a simple product manager for file:// protocol
                window.adminManager = new FileProtocolProductManager();
                console.log('File protocol product manager initialized');
            }

            // Initialize page
            initializeProductsPage();
            setupEventListeners();

            console.log('Admin products page initialization complete');
        }

        // File Protocol Compatible Product Manager
        class FileProtocolProductManager {
            constructor() {
                this.products = this.loadProducts();
                console.log('FileProtocolProductManager initialized with', this.products.length, 'products');
            }

            loadProducts() {
                try {
                    const products = localStorage.getItem('vaith_products');
                    return products ? JSON.parse(products) : [];
                } catch (error) {
                    console.error('Error loading products:', error);
                    return [];
                }
            }

            saveProducts() {
                try {
                    localStorage.setItem('vaith_products', JSON.stringify(this.products));
                    console.log('Products saved to localStorage');
                    return true;
                } catch (error) {
                    console.error('Error saving products:', error);
                    return false;
                }
            }

            getAllProducts() {
                return this.products;
            }

            getProductById(id) {
                return this.products.find(product => product.id === parseInt(id));
            }



            updateProduct(id, productData) {
                const index = this.products.findIndex(product => product.id === parseInt(id));
                if (index !== -1) {
                    this.products[index] = {
                        ...this.products[index],
                        ...productData,
                        updatedDate: new Date().toISOString()
                    };
                    this.saveProducts();
                    return this.products[index];
                }
                return null;
            }

            deleteProduct(id) {
                const index = this.products.findIndex(product => product.id === parseInt(id));
                if (index !== -1) {
                    const deletedProduct = this.products.splice(index, 1)[0];
                    this.saveProducts();
                    return deletedProduct;
                }
                return null;
            }

            getProductStats() {
                const total = this.products.length;
                const active = this.products.filter(p => p.status === 'active').length;
                const outOfStock = this.products.filter(p => p.stock === 0).length;
                const lowStock = this.products.filter(p => p.stock > 0 && p.stock <= 5).length;

                return {
                    total,
                    active,
                    outOfStock,
                    lowStock,
                    activePercentage: total > 0 ? Math.round((active / total) * 100) : 0
                };
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            // Check if we're in file:// protocol or have auth manager
            if (typeof authManager !== 'undefined' && authManager.requireAdmin && !authManager.requireAdmin()) {
                console.log('Admin access denied, redirecting...');
                return;
            }

            // Initialize with file protocol compatibility
            console.log('Initializing admin products...');
            initializeAdminProducts();
        });

        // Fallback initialization in case auth manager blocks the main initialization
        setTimeout(function() {
            const addProductBtn = document.getElementById('addProductBtn');
            if (addProductBtn && !addProductBtn.hasAttribute('data-initialized')) {
                console.log('Fallback initialization triggered');
                setupEventListeners();
                addProductBtn.setAttribute('data-initialized', 'true');
            }
        }, 1000);

        // Test function for debugging - can be called from browser console
        window.testAddProductModal = function() {
            console.log('Testing add product modal...');
            showAddProductModal();
        };

        // Make functions globally available for debugging
        window.showAddProductModal = showAddProductModal;
        window.closeAddProductModal = closeAddProductModal;

        function initializeProductsPage() {
            loadProductStats();
            loadProducts();
        }

        function loadProductStats() {
            const stats = adminManager.getProductStats();
            document.getElementById('totalProductsCount').textContent = stats.total;
            document.getElementById('activeProductsCount').textContent = stats.active;
            document.getElementById('outOfStockCount').textContent = stats.outOfStock;
            document.getElementById('lowStockCount').textContent = stats.lowStock;
        }

        function loadProducts() {
            console.log('Loading products...');
            if (typeof adminManager === 'undefined') {
                console.error('AdminManager not available');
                return;
            }

            currentProducts = adminManager.getAllProducts();
            filteredProducts = [...currentProducts];
            console.log('Loaded products:', currentProducts.length);
            renderProductsTable();
        }

        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');
            
            if (filteredProducts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 3rem; color: var(--admin-text-tertiary);">
                            <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                                <i class="fas fa-box-open" style="font-size: 3rem; opacity: 0.5;"></i>
                                <div>
                                    <div style="font-weight: 600; margin-bottom: 0.5rem; font-size: 1.1rem;">No products found</div>
                                    <div style="font-size: 0.875rem;">No products are currently available in the store</div>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredProducts.map(product => `
                <tr>
                    <td>
                        <div>
                            <div style="font-weight: 500; margin-bottom: 0.25rem;">${product.name}</div>
                            <div style="font-size: 0.75rem; color: var(--text-light);">${product.brand}</div>
                        </div>
                    </td>
                    <td style="font-family: monospace;">${product.sku}</td>
                    <td><span class="status-badge status-active">${product.category}</span></td>
                    <td>
                        <div style="font-weight: 500;">$${product.price}</div>
                        ${product.originalPrice && product.originalPrice !== product.price ? 
                            `<div style="font-size: 0.75rem; color: var(--text-light); text-decoration: line-through;">$${product.originalPrice}</div>` : 
                            ''
                        }
                    </td>
                    <td>
                        <div class="stock-indicator">
                            <div class="stock-dot ${getStockIndicatorClass(product.stock)}"></div>
                            <span>${product.stock}</span>
                        </div>
                    </td>
                    <td><span class="status-badge ${getProductStatusBadgeClass(product.status)}">${product.status}</span></td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 0.25rem;">
                            <span style="color: #fbbf24;">★</span>
                            <span>${product.rating.toFixed(1)}</span>
                            <span style="color: var(--text-light); font-size: 0.75rem;">(${product.reviews})</span>
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-secondary btn-sm" onclick="viewProduct(${product.id})" title="View Product Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="editProduct(${product.id})" title="Edit Product">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})" title="Delete Product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');

                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');
            });

            // Search functionality
            document.getElementById('productSearch').addEventListener('input', applyFilters);

            // Filter functionality
            document.getElementById('categoryFilter').addEventListener('change', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);

            // Add Product Button
            const addProductBtn = document.getElementById('addProductBtn');
            if (addProductBtn && !addProductBtn.hasAttribute('data-initialized')) {
                addProductBtn.addEventListener('click', function() {
                    console.log('Add Product button clicked');
                    showAddProductModal();
                });
                addProductBtn.setAttribute('data-initialized', 'true');
                console.log('Add Product button event listener attached');
            } else if (!addProductBtn) {
                console.error('Add Product button not found');
            } else {
                console.log('Add Product button already initialized');
            }

            // Add Product Modal Events
            document.getElementById('closeAddProductModal').addEventListener('click', closeAddProductModal);
            document.getElementById('cancelAddProduct').addEventListener('click', closeAddProductModal);
            document.getElementById('addProductForm').addEventListener('submit', handleAddProduct);

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Modal close
            document.getElementById('closeProductModal').addEventListener('click', closeModal);

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                authManager.logout();
            });
        }

        function applyFilters() {
            const searchQuery = document.getElementById('productSearch').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredProducts = currentProducts.filter(product => {
                const matchesSearch = !searchQuery || 
                    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    product.brand.toLowerCase().includes(searchQuery.toLowerCase());

                const matchesCategory = !categoryFilter || product.category === categoryFilter;
                const matchesStatus = !statusFilter || product.status === statusFilter;

                return matchesSearch && matchesCategory && matchesStatus;
            });

            renderProductsTable();
        }

        function getStockIndicatorClass(stock) {
            if (stock === 0) return 'stock-out';
            if (stock <= 5) return 'stock-low';
            if (stock <= 20) return 'stock-medium';
            return 'stock-high';
        }

        function viewProduct(productId) {
            const product = adminManager.getProductById(productId);
            if (!product) return;

            document.getElementById('modalTitle').textContent = 'Product Details';
            document.getElementById('productModalBody').innerHTML = `
                <div style="display: grid; gap: 1.5rem;">
                    <div>
                        <h3 style="margin-bottom: 0.5rem;">${product.name}</h3>
                        <p style="color: var(--text-light); margin-bottom: 1rem;">${product.description}</p>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div><strong>Brand:</strong> ${product.brand}</div>
                            <div><strong>SKU:</strong> ${product.sku}</div>
                            <div><strong>Category:</strong> ${product.category}</div>
                            <div><strong>Status:</strong> <span class="status-badge ${getProductStatusBadgeClass(product.status)}">${product.status}</span></div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                        <div>
                            <strong>Price:</strong><br>
                            <span style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">$${product.price}</span>
                            ${product.originalPrice && product.originalPrice !== product.price ? 
                                `<span style="text-decoration: line-through; color: var(--text-light); margin-left: 0.5rem;">$${product.originalPrice}</span>` : 
                                ''
                            }
                        </div>
                        <div>
                            <strong>Stock:</strong><br>
                            <div class="stock-indicator" style="margin-top: 0.25rem;">
                                <div class="stock-dot ${getStockIndicatorClass(product.stock)}"></div>
                                <span>${product.stock} units</span>
                            </div>
                        </div>
                        <div>
                            <strong>Rating:</strong><br>
                            <div style="display: flex; align-items: center; gap: 0.25rem; margin-top: 0.25rem;">
                                <span style="color: #fbbf24;">★</span>
                                <span>${product.rating.toFixed(1)}</span>
                                <span style="color: var(--text-light);">(${product.reviews} reviews)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <strong>Available Sizes:</strong><br>
                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${product.sizes.map(size => `<span class="status-badge status-active">${size}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Available Colors:</strong><br>
                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            ${product.colors.map(color => `<span class="status-badge status-pending">${color}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <strong>Created:</strong> ${formatDate(product.createdDate)}<br>
                        <strong>Last Updated:</strong> ${formatDate(product.updatedDate)}
                    </div>
                </div>
            `;
            
            showModal();
        }

        function editProduct(productId) {
            // For now, just show view - in a real app, this would open an edit form
            viewProduct(productId);
        }

        function deleteProduct(productId) {
            const product = adminManager.getProductById(productId);
            if (!product) return;

            if (confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
                adminManager.deleteProduct(productId);
                loadProducts();
                loadProductStats();
            }
        }

        function showModal() {
            document.getElementById('productModal').style.display = 'flex';
            document.getElementById('overlay').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';
        }

        function showAddProductModal() {
            console.log('showAddProductModal function called');

            const modal = document.getElementById('addProductModal');
            const overlay = document.getElementById('overlay');

            if (modal && overlay) {
                modal.style.display = 'flex';
                overlay.style.display = 'block';
                console.log('Modal and overlay displayed');

                // Generate SKU
                const sku = generateSKU();
                const skuInput = document.getElementById('productSku');
                if (skuInput) {
                    skuInput.value = sku;
                    console.log('SKU generated:', sku);
                } else {
                    console.error('SKU input not found');
                }
            } else {
                console.error('Modal or overlay not found', { modal, overlay });
            }
        }

        function closeAddProductModal() {
            document.getElementById('addProductModal').style.display = 'none';
            document.getElementById('overlay').style.display = 'none';

            // Reset form
            document.getElementById('addProductForm').reset();
        }

        function generateSKU() {
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.random().toString(36).substring(2, 5).toUpperCase();
            return `SKU-${random}${timestamp}`;
        }

        function handleAddProduct(e) {
            e.preventDefault();

            // Get form data
            const formData = {
                name: document.getElementById('productName').value.trim(),
                brand: document.getElementById('productBrand').value.trim() || 'VAITH',
                description: document.getElementById('productDescription').value.trim(),
                category: document.getElementById('productCategory').value,
                status: document.getElementById('productStatus').value,
                sku: document.getElementById('productSku').value.trim(),
                price: parseFloat(document.getElementById('productPrice').value),
                originalPrice: document.getElementById('productOriginalPrice').value ?
                    parseFloat(document.getElementById('productOriginalPrice').value) : null,
                stock: parseInt(document.getElementById('productStock').value),
                images: document.getElementById('productImages').value.trim().split('\n')
                    .map(url => url.trim()).filter(url => url),
                sizes: document.getElementById('productSizes').value.trim().split(',')
                    .map(size => size.trim()).filter(size => size),
                colors: document.getElementById('productColors').value.trim().split(',')
                    .map(color => color.trim()).filter(color => color)
            };

            // Validate required fields
            if (!formData.name || !formData.category || !formData.price || formData.stock < 0) {
                showFileProtocolToast('Please fill in all required fields', 'error');
                return;
            }

            // Add default image if none provided
            if (formData.images.length === 0) {
                formData.images = ['https://via.placeholder.com/400x500?text=No+Image'];
            }

            // Add default sizes and colors if none provided
            if (formData.sizes.length === 0) {
                formData.sizes = ['One Size'];
            }
            if (formData.colors.length === 0) {
                formData.colors = ['Default'];
            }

            // Create product object
            const product = {
                id: Date.now(), // Simple ID generation
                ...formData,
                rating: 0,
                reviews: 0,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString()
            };

            // Add product using admin manager
            if (adminManager.addProduct) {
                adminManager.addProduct(product);
            } else {
                // Fallback for file protocol
                adminManager.products.push(product);
                adminManager.saveProducts();
            }

            // Show success message
            showFileProtocolToast(`Product "${formData.name}" added successfully!`, 'success');

            // Close modal and refresh
            closeAddProductModal();
            loadProducts();
            loadProductStats();
        }





        // File Protocol Compatible Toast Notification System
        function showFileProtocolToast(message, type = 'info') {
            // Create toast container if it doesn't exist
            let container = document.querySelector('.file-protocol-toast-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'file-protocol-toast-container';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    pointer-events: none;
                `;
                document.body.appendChild(container);
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = `file-protocol-toast file-protocol-toast-${type}`;

            // Toast styles
            const baseStyles = `
                background: white;
                border-radius: 8px;
                padding: 16px 20px;
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border-left: 4px solid;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                font-weight: 500;
                max-width: 400px;
                word-wrap: break-word;
                pointer-events: auto;
                cursor: pointer;
                transform: translateX(100%);
                transition: transform 0.3s ease-in-out;
                display: flex;
                align-items: center;
                gap: 10px;
            `;

            let typeStyles = '';
            let icon = '';

            switch (type) {
                case 'success':
                    typeStyles = 'border-left-color: #10b981; color: #065f46;';
                    icon = '✅';
                    break;
                case 'error':
                    typeStyles = 'border-left-color: #ef4444; color: #991b1b;';
                    icon = '❌';
                    break;
                case 'warning':
                    typeStyles = 'border-left-color: #f59e0b; color: #92400e;';
                    icon = '⚠️';
                    break;
                default:
                    typeStyles = 'border-left-color: #3b82f6; color: #1e40af;';
                    icon = 'ℹ️';
            }

            toast.style.cssText = baseStyles + typeStyles;
            toast.innerHTML = `<span>${icon}</span><span>${message}</span>`;

            // Add to container
            container.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // Auto remove after 5 seconds
            const removeToast = () => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.parentElement.removeChild(toast);
                    }
                }, 300);
            };

            // Click to dismiss
            toast.addEventListener('click', removeToast);

            // Auto dismiss
            setTimeout(removeToast, 5000);
        }

        // Close modals when clicking overlay
        document.getElementById('overlay').addEventListener('click', function() {
            // Check which modal is open and close the appropriate one
            const productModal = document.getElementById('productModal');
            const addProductModal = document.getElementById('addProductModal');

            if (productModal.style.display === 'flex') {
                closeModal();
            } else if (addProductModal.style.display === 'flex') {
                closeAddProductModal();
            }
        });
    </script>


</body>
</html>
